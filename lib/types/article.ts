// 统一的文章相关类型定义

export interface ContentData {
  id?: number;
  title?: string;
  fileUrl?: string;
  spaceId?: number;
  createAt?: string;
  updateAt?: string;
  type?: 'video' | 'audio' | 'doc';
}

export interface ChapterSection {
  id: string;
  pageNumber: number;
  title: string;
  content: string;
}

export interface NewArticleChat {
  modelId: string;
  aiQuery: string;
  type?: 'video' | 'audio' | 'doc';
}

export type RequestType = 'flashcards' | 'chapters' | 'summary' | 'quizGroups';

// 自动发送相关类型
export interface AutoSendData {
  initialInputValue: string;
  initialSelectedModelId: string;
  autoSend: boolean;
  type?: 'video' | 'audio' | 'doc';
}

// 组件 Props 类型
export interface RightSidebarProps {
  tabs: { id: string; label: string; icon: any }[];
  activeTab: string;
  setActiveTab: (tabId: string) => void;
  contentId?: number;
  showArticleOnDesktop?: boolean;
  onToggleArticle?: () => void;
  newArticleChatData?: AutoSendData | null;
  onAutoSendComplete?: () => void;
  hasValidPdf?: boolean;
  contentData?: ContentData;
}

export interface ChatComponentProps {
  contentId: number;
  initialInputValue?: string;
  initialSelectedModelId?: string;
  autoSend?: boolean;
  onAutoSendComplete?: () => void;
  contentData?: ContentData;
}
