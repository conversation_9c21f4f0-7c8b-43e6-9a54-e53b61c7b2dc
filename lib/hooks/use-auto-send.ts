import { useState, useEffect, useCallback } from 'react';
import { useNewArticleChatStore } from '@/lib/stores/article-cache-store';
import type { ContentData, AutoSendData } from '@/lib/types/article';

interface UseAutoSendOptions {
  contentId: number;
  contentData?: ContentData;
  onAutoSendComplete?: () => void;
}

interface UseAutoSendReturn {
  autoSendData: AutoSendData | null;
  handleAutoSendComplete: () => void;
  shouldAutoSend: boolean;
}

/**
 * 统一的自动发送逻辑 Hook
 * 处理视频、音频、文档等不同类型内容的自动发送逻辑
 */
export function useAutoSend({
  contentId,
  contentData,
  onAutoSendComplete
}: UseAutoSendOptions): UseAutoSendReturn {
  const { getNewArticleChat, clearNewArticleChat } = useNewArticleChatStore();
  const [autoSendData, setAutoSendData] = useState<AutoSendData | null>(null);

  // 检查是否需要自动发送
  const shouldAutoSend = Boolean(
    autoSendData?.autoSend &&
    (autoSendData.initialInputValue ||
     contentData?.type === 'video' ||
     contentData?.type === 'audio')
  );

  // 调试日志
  useEffect(() => {
    if (autoSendData) {
      console.log('🔍 useAutoSend state:', {
        autoSendData,
        shouldAutoSend,
        contentType: contentData?.type,
        hasFileUrl: Boolean(contentData?.fileUrl)
      });
    }
  }, [autoSendData, shouldAutoSend, contentData]);

  // 处理自动发送完成
  const handleAutoSendComplete = useCallback(() => {
    if (contentId) {
      clearNewArticleChat(contentId);
    }
    setAutoSendData(null);
    onAutoSendComplete?.();
  }, [contentId, clearNewArticleChat, onAutoSendComplete]);

  // 监听 contentData 变化，处理视频/音频自动描述
  useEffect(() => {
    if (!contentData || !contentId) return;

    // 检查是否为视频类型且没有fileUrl，自动触发视频描述
    if (contentData.type === 'video' && !contentData.fileUrl) {
      console.log('🎥 Video content detected without fileUrl, triggering auto description');
      setAutoSendData({
        initialInputValue: '',
        initialSelectedModelId: '',
        autoSend: true,
        type: 'video'
      });
      return;
    }

    // 检查是否为音频类型且没有fileUrl，自动触发音频描述
    if (contentData.type === 'audio' && !contentData.fileUrl) {
      console.log('🎵 Audio content detected without fileUrl, triggering auto description');
      setAutoSendData({
        initialInputValue: '',
        initialSelectedModelId: '',
        autoSend: true,
        type: 'audio'
      });
      return;
    }
  }, [contentData, contentId]);

  // 监听 newArticleChat 数据变化
  useEffect(() => {
    if (!contentId) return;

    const chatData = getNewArticleChat(contentId);

    if (chatData?.modelId && chatData?.aiQuery) {
      console.log('🔍 Setting up new article chat:', {
        contentId,
        aiQuery: chatData.aiQuery,
        modelId: chatData.modelId
      });

      setAutoSendData({
        initialInputValue: chatData.aiQuery,
        initialSelectedModelId: chatData.modelId,
        autoSend: true,
        type: chatData.type
      });
    }
  }, [contentId, getNewArticleChat]);

  return {
    autoSendData,
    handleAutoSendComplete,
    shouldAutoSend
  };
}

/**
 * 获取自动发送的消息内容
 */
export function getAutoSendMessage(autoSendData: AutoSendData | null): string {
  if (!autoSendData) return '';

  // 对于视频和音频类型，使用默认的描述请求文本
  if (autoSendData.type === 'video') {
    return '请分析这个视频内容';
  }
  
  if (autoSendData.type === 'audio') {
    return '请分析这个音频内容';
  }

  // 对于其他类型，使用提供的输入值
  return autoSendData.initialInputValue || '';
}

/**
 * 检查是否应该显示用户消息
 * 对于视频和音频的自动发送，不显示用户消息
 */
export function shouldShowUserMessage(autoSendData: AutoSendData | null): boolean {
  if (!autoSendData?.autoSend) return true;
  
  return !(autoSendData.type === 'video' || autoSendData.type === 'audio');
}

/**
 * 获取自动发送的 endpoint
 */
export function getAutoSendEndpoint(autoSendData: AutoSendData | null, contentData?: ContentData): string | undefined {
  if (!autoSendData?.autoSend || !contentData?.type) return undefined;

  switch (contentData.type) {
    case 'video':
      if (!contentData.fileUrl) {
        return '/llm/video/describe';
      }
      break;
    case 'audio':
      if (!contentData.fileUrl) {
        return '/llm/audio/describe';
      }
      break;
  }

  return undefined;
}
