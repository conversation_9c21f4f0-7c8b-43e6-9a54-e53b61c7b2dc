'use client';
import { useState, useRef, useEffect, useCallback } from 'react';
import { useAutoSend, getAutoSendMessage, shouldShowUserMessage, getAutoSendEndpoint } from '@/lib/hooks/use-auto-send';

// 全局状态管理，防止重复执行
const globalAutoSendState = {
  executing: new Set<string>(),
  completed: new Set<string>()
};
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessageItem } from '@/components/chat/chat-message-item';
import { ChatInputArea } from '@/components/chat/chat-input-area';
import { ChatLoadingIndicator } from '@/components/chat/chat-loading-indicator';
import { CollectNoteModal } from '@/components/notes/collect-note-modal';
import { MessageCircle, HelpCircle, Brain, MicIcon, Layers, Search, Calendar, Plus } from 'lucide-react';
import { postLlmChatContentId } from '@/servers/api/wendangneirongliaotian';
import { postLlmChatContentIdStream } from '@/lib/api/streaming';
import { getContentNewSessionId } from '@/servers/api/wendangneirong';
import { useAppDataStore, type Model } from '@/lib/stores/app-data-store';
import type { ChatMessage } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { saveChatToStorage, loadChatFromStorage, clearChatFromStorage } from '@/lib/utils/chat-storage';
import { useAppStore } from '@/lib/stores';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/api/hooks';

import type { ChatComponentProps } from '@/lib/types/article';

export function ChatComponent({
  contentId,
  initialInputValue,
  initialSelectedModelId,
  autoSend = false,
  onAutoSendComplete,
  contentData
}: ChatComponentProps) {
  const { t } = useTranslation();
  const { models } = useAppDataStore();
  const queryClient = useQueryClient();

  // 使用统一的自动发送 hook
  const { autoSendData, handleAutoSendComplete, shouldAutoSend } = useAutoSend({
    contentId,
    contentData,
    onAutoSendComplete
  });

  // 基础状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  console.log('🚀 ~ messages:', messages);

  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasAutoSent, setHasAutoSent] = useState(false); // 防止重复自动发送

  // UI 状态
  const [isCollectModalOpen, setIsCollectModalOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [showGlobe, setShowGlobe] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Array<{ id: number; name: string; size: number }>>([]);

  const { openUpgradeModal } = useAppStore();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 从本地存储加载聊天记录
  useEffect(() => {
    if (contentId) {
      const storedMessages = loadChatFromStorage('content', contentId);
      setMessages(storedMessages);
      // 重置自动发送状态，允许新内容的自动发送
      setHasAutoSent(false);
      console.log('💾 Loaded chat messages for contentId:', contentId, 'messages count:', storedMessages.length);
    }
  }, [contentId]);

  // 保存聊天记录到本地存储
  useEffect(() => {
    console.log('💾 Save effect triggered:', { contentId, messagesLength: messages.length, messages });
    if (contentId && messages.length > 0) {
      saveChatToStorage('content', contentId, messages);
      console.log('💾 Saved messages to storage');
    }
  }, [contentId, messages]);

  // 创建新聊天 - 清空聊天记录和本地存储
  const handleNewChat = useCallback(async () => {
    try {
      // 先调用新建会话接口
      await getContentNewSessionId({ id: contentId });

      // 清空聊天记录和本地存储
      setMessages([]);
      setInputValue('');
      if (contentId) {
        clearChatFromStorage('content', contentId);
      }
    } catch (error: any) {
      console.error('New session creation failed:', error);
      toast.error(error?.msg || error?.message || t('common.error'));
    }
  }, [contentId, t]);

  // 移除了初始化逻辑，现在由 HybridAuthProvider 统一管理

  // 简化的模型设置逻辑
  useEffect(() => {
    if (models.length > 0 && !selectedModel) {
      if (initialSelectedModelId) {
        const foundModel = models.find(
          (m) => m.id?.toString() === initialSelectedModelId || m.name === initialSelectedModelId
        );
        setSelectedModel(foundModel || models[0]);
      } else {
        setSelectedModel(models[0]);
      }
    }
  }, [models, selectedModel, initialSelectedModelId]);

  // 发送消息的核心逻辑 - 支持传入参数或使用当前状态
  const handleSendMessage = useCallback(
    async (messageText?: string, model?: Model) => {
      const textToSend = messageText || inputValue.trim();

      console.log('🚀 handleSendMessage:', {
        textToSend,
        isAutoSend: !!messageText,
        modelToUse: model?.name || selectedModel?.name,
        autoSendData,
        contentType: contentData?.type
      });

      const modelToUse = model || selectedModel;
      const isAutoSend = !!messageText; // 如果传入了 messageText，说明是自动发送

      // 根据内容类型确定 endpoint
      const endpoint = isAutoSend ? getAutoSendEndpoint(autoSendData, contentData) : undefined;
      const autoSendType = autoSendData?.type;

      console.log('📡 Endpoint and type:', { endpoint, autoSendType, isAutoSend });

      // 检查是否应该显示用户消息
      const shouldShowMessage = isAutoSend ? shouldShowUserMessage(autoSendData) : true;

      console.log('👤 User message display check:', { shouldShowMessage, isAutoSend, autoSendData });

      if (shouldShowMessage) {
        const newMessage: ChatMessage = {
          id: String(Date.now()),
          sender: 'user',
          text: textToSend,
          timestamp: new Date()
        };

        console.log('➕ Adding user message:', newMessage);
        setMessages((prev) => {
          const newMessages = [...prev, newMessage];
          console.log('📝 Messages after adding user message:', newMessages);
          return newMessages;
        });
      }

      // 只有在非自动发送时才清空输入框
      if (!isAutoSend) {
        setInputValue('');
      }

      setIsLoading(true);

      // 创建AI响应消息
      const aiMessageId = String(Date.now() + 1);
      const aiResponse: ChatMessage = {
        id: aiMessageId,
        sender: 'ai',
        text: '',
        timestamp: new Date()
      };

      console.log('🤖 Creating AI response message:', aiMessageId);
      setMessages((prev) => {
        const newMessages = [...prev, aiResponse];
        console.log('📝 Messages after adding AI message:', newMessages);
        return newMessages;
      });

      try {
        await postLlmChatContentIdStream(
          {
            contentId,
            question: textToSend,
            modelId: modelToUse?.id,
            uploadId: uploadedFiles.map((file) => file.id)
          },
          (chunk: string) => {
            // 直接更新消息内容，保持加载状态直到完成
            console.log('📝 Received chunk:', chunk.length, 'chars');
            setMessages((prev) => {
              const updatedMessages = prev.map((msg) => {
                if (msg.id === aiMessageId) {
                  const updatedMsg = { ...msg, text: msg.text + chunk };
                  console.log('🔄 Updating AI message:', updatedMsg.text.length, 'total chars');
                  return updatedMsg;
                }
                return msg;
              });
              return updatedMessages;
            });
          },
          (error: Error & { code?: string; msg?: string }) => {
            if (error?.code === '30501') {
              openUpgradeModal();
            }
            // 🚨 改进错误处理：显示具体的错误信息
            const errorMessage = error.message || error?.msg || t('common.error');

            setMessages((prev) =>
              prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: `❌ ${errorMessage}` } : msg))
            );
            // 显示具体的错误信息给用户
            toast.error(errorMessage);
            setIsLoading(false);
          },
          () => {
            // 确保加载状态被正确清除（防止没有数据的情况）
            console.log('✅ Stream completed, clearing loading state');
            setIsLoading(false);

            // 如果是 video 或 audio 类型的自动发送，完成后刷新 contentData
            if (isAutoSend && endpoint && (autoSendType === 'video' || autoSendType === 'audio')) {
              console.log(`🔄 Refreshing contentData for ${autoSendType} type after auto-send completion`);
              queryClient.invalidateQueries({
                queryKey: queryKeys.contentDetail(contentId)
              });
            }
          },
          undefined, // options
          undefined, // completionConfig
          endpoint // 传入 endpoint 参数
        );
      } catch (error: any) {
        console.error('Chat API error:', error);
        setMessages((prev) => prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: t('common.error') } : msg)));
        toast.error(t('common.error'));
        setIsLoading(false);
        throw error;
      }
    },
    [inputValue, selectedModel, contentId, isLoading, uploadedFiles, contentData?.type, contentData?.fileUrl, queryClient]
  );

  // 自动发送逻辑 - 使用全局状态防止重复执行
  const executeAutoSend = useCallback(async () => {
    if (!autoSendData) return;

    const autoSendKey = `${contentId}-${autoSendData.initialInputValue || 'auto'}-${autoSendData.type || 'default'}`;

    // 使用全局状态进行原子性检查
    if (globalAutoSendState.executing.has(autoSendKey) || globalAutoSendState.completed.has(autoSendKey)) {
      console.log('⏭️ Auto-send already executed or executing for key:', autoSendKey);
      return;
    }

    console.log('🚀 Starting auto-send for contentId:', contentId, 'type:', autoSendData.type, 'key:', autoSendKey);

    // 立即标记为执行中，防止重复
    globalAutoSendState.executing.add(autoSendKey);
    setHasAutoSent(true);

    try {
      // 使用统一的消息获取逻辑
      const messageToSend = getAutoSendMessage(autoSendData);
      console.log('📤 Auto-sending message:', messageToSend);
      await handleSendMessage(messageToSend, selectedModel || undefined);

      // 标记为已完成
      globalAutoSendState.completed.add(autoSendKey);
      handleAutoSendComplete();
      onAutoSendComplete?.();
    } catch (error) {
      console.error('Auto-send failed:', error);
      setHasAutoSent(false); // 失败时重置状态，允许重试
      handleAutoSendComplete();
      onAutoSendComplete?.();
    } finally {
      // 从执行中移除
      globalAutoSendState.executing.delete(autoSendKey);
    }
  }, [contentId, autoSendData, selectedModel, handleSendMessage, handleAutoSendComplete]);

  // 简化的自动发送触发逻辑
  useEffect(() => {
    // 检查是否需要自动发送
    const shouldTriggerAutoSend = shouldAutoSend && selectedModel && !isLoading && !hasAutoSent;

    if (shouldTriggerAutoSend) {
      console.log('🎯 Triggering auto-send:', { shouldAutoSend, selectedModel: selectedModel?.name, isLoading, hasAutoSent });
      executeAutoSend();
    }
  }, [shouldAutoSend, selectedModel, isLoading, hasAutoSent, executeAutoSend]);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
        if (scrollElement) {
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  }, []);

  // 当消息更新时自动滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const copyMessage = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t('chat.copySuccess'));
  };

  const handleCollectNote = (text: string) => {
    setSelectedText(text);
    setIsCollectModalOpen(true);
  };

  // Quick action buttons data
  const quickActions = [
    {
      icon: HelpCircle,
      label: t('article.chat.quickActions.quiz'),
      prompt: t('article.chat.quickActions.quizPrompt'),
      color: 'bg-blue-50 hover:bg-blue-100 text-blue-700'
    },
    {
      icon: Brain,
      label: t('article.chat.quickActions.mindMap'),
      prompt: t('article.chat.quickActions.mindMapPrompt'),
      color: 'bg-purple-50 hover:bg-purple-100 text-purple-700'
    },
    {
      icon: Layers,
      label: t('article.chat.quickActions.flashcards'),
      prompt: t('article.chat.quickActions.flashcardsPrompt'),
      color: 'bg-orange-50 hover:bg-orange-100 text-orange-700'
    },
    {
      icon: Calendar,
      label: t('article.chat.quickActions.schedule'),
      prompt: t('article.chat.quickActions.schedulePrompt'),
      color: 'bg-indigo-50 hover:bg-indigo-100 text-indigo-700'
    }
  ];

  const handleQuickActionClick = (prompt: string) => {
    setInputValue(prompt);
    setTimeout(() => {
      const inputElement = document.querySelector(
        'textarea[placeholder*="' + t('article.chat.inputPlaceholder') + '"]'
      ) as HTMLTextAreaElement;
      if (inputElement) {
        inputElement.focus();
      }
    }, 100);
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-neutral-900">
      {/* New Chat Button - 只在有聊天记录时显示 */}
      {messages.length > 0 && (
        <div className="flex-shrink-0 p-3 border-b dark:border-neutral-700">
          <Button
            onClick={handleNewChat}
            variant="outline"
            size="sm"
            className="w-full h-8 text-sm font-medium gap-2 hover:bg-gray-50 dark:hover:bg-neutral-800"
          >
            <Plus className="w-4 h-4" />
            {t('article.chat.newChat')}
          </Button>
        </div>
      )}

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4 overflow-hidden" ref={scrollAreaRef}>
        {messages.length === 0 ? (
          // Empty State
          <div className="flex flex-col items-center justify-center h-full py-8">
            <div className="mb-6">
              <div className="w-20 h-20 bg-gray-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
                <MessageCircle className="w-10 h-10 text-gray-400" strokeWidth={1.5} />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-8 text-center">
              {t('article.chat.title')}
            </h3>

            <div className="grid grid-cols-2 gap-3 w-full max-w-xs">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  onClick={() => handleQuickActionClick(action.prompt)}
                  className={`h-12 flex items-center justify-center gap-2 text-sm font-medium border-gray-200 dark:border-neutral-700 ${action.color} dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:text-gray-300`}
                >
                  <action.icon className="w-4 h-4" />
                  {action.label}
                </Button>
              ))}
            </div>
          </div>
        ) : (
          // Messages Display
          <div className="space-y-3 md:space-y-4">
            {messages.map((msg, index) => (
              <ChatMessageItem
                key={msg.id}
                message={msg}
                onCopyMessage={copyMessage}
                handleCollectNote={handleCollectNote}
                isLoading={isLoading && msg.sender === 'ai' && index === messages.length - 1}
                index={index}
              />
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <ChatInputArea
        inputValue={inputValue}
        showGlobe={showGlobe}
        setShowGlobe={setShowGlobe}
        placeholder={t('article.chat.inputPlaceholder')}
        onInputValueChange={setInputValue}
        onSendMessage={() => handleSendMessage()}
        selectedMode={selectedModel?.name || 'Default'}
        onModeChange={(modelName: string) => {
          const foundModel = models.find((m) => m.name === modelName);
          if (foundModel) {
            setSelectedModel(foundModel);
          }
        }}
        isLoading={isLoading}
        availableModels={models}
        uploadedFiles={uploadedFiles}
        onUploadedFilesChange={setUploadedFiles}
      />

      <CollectNoteModal
        isOpen={isCollectModalOpen}
        onClose={() => setIsCollectModalOpen(false)}
        selectedText={selectedText}
        contentId={contentId}
        mode="create-with-content"
      />
    </div>
  );
}
