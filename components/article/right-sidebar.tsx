'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ChatComponent } from './chat-component';
import { FlashcardsComponent } from './flashcards-component';
import { QuizComponent } from './quiz-component';
import { SummaryComponent } from './summary-component';
import { ChaptersComponent } from './chapters-component';
import { Button } from '@/components/ui/button';
import {
  MessageCircle,
  Layers,
  HelpCircle,
  FileText,
  BookOpen,
  ChevronLeft,
  Share,
  MessageSquare,
  ChevronRight
} from 'lucide-react';
import type { Chapter, Flashcard, QuizQuestion } from '@/types';
import type { LucideIcon } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useRef, useEffect, useState, useCallback } from 'react';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';

interface ChapterSection {
  id: string;
  pageNumber: number;
  title: string;
  content: string;
}

interface RightSidebarProps {
  tabs: { id: string; label: string; icon: LucideIcon }[];
  activeTab: string;
  setActiveTab: (tabId: string) => void;
  contentId?: number;
  showArticleOnDesktop?: boolean;
  onToggleArticle?: () => void;
  newArticleChatData?: {
    initialInputValue: string;
    initialSelectedModelId: string;
    autoSend: boolean;
    type?: 'video' | 'audio' | 'doc';
  } | null;
  onAutoSendComplete?: () => void;
  hasValidPdf?: boolean;
  contentData?: any; // 添加 contentData 参数
}

export function RightSidebar({
  tabs,
  activeTab,
  setActiveTab,
  contentId,
  showArticleOnDesktop = true,
  onToggleArticle,
  newArticleChatData,
  onAutoSendComplete,
  hasValidPdf = true,
  contentData
}: RightSidebarProps) {
  const { t } = useTranslation();

  // 使用缓存store - 统一管理请求状态
  const { 
    hasFlashcards, 
    hasChapters, 
    hasSummary, 
    hasQuizGroups,
    shouldStartRequest,
    setRequestingState,
    isRequesting
  } = useArticleCacheStore();

  // 跟踪当前是否应该加载的状态
  const [shouldLoadStates, setShouldLoadStates] = useState<Record<string, boolean>>({});

  // 跟踪已访问的tab，只为访问过的tab加载数据
  const [visitedTabs, setVisitedTabs] = useState<Set<string>>(new Set());

  // 当tab切换时，标记为已访问并触发数据加载
  const handleInternalTabChange = useCallback((tabId: string) => {
    // 只有当 tab 真的发生变化时才处理
    if (activeTab === tabId) return;

    setActiveTab(tabId);

    // 标记tab为已访问
    setVisitedTabs((prev) => new Set([...prev, tabId]));

    // 只为新访问的tab加载数据，并且只对非 chat tab 进行数据加载
    if (contentId && tabId !== 'chat') {
      triggerDataLoad(tabId);
    }
  }, [activeTab, setActiveTab, contentId]);

  // 触发数据加载 - 使用store的shouldStartRequest方法避免重复请求
  const triggerDataLoad = useCallback((tabId: string) => {
    if (!contentId) return;

    let requestType: 'flashcards' | 'chapters' | 'summary' | 'quizGroups' | null = null;
    
    switch (tabId) {
      case 'flashcards':
        requestType = 'flashcards';
        break;
      case 'summary':
        requestType = 'summary';
        break;
      case 'chapters':
        requestType = 'chapters';
        break;
      case 'quiz':
        requestType = 'quizGroups';
        break;
    }

    // 使用store的shouldStartRequest方法判断是否应该发起请求
    if (requestType && shouldStartRequest(contentId, requestType)) {
      console.log(`🚀 RightSidebar: Starting request for ${requestType}, contentId: ${contentId}`);
      setRequestingState(contentId, requestType, true);
      // 设置应该加载的状态
      setShouldLoadStates(prev => ({ ...prev, [requestType]: true }));
    } else if (requestType) {
      console.log(`⏭️ RightSidebar: Skipping request for ${requestType} (already has data or requesting)`);
    }
  }, [contentId, shouldStartRequest, setRequestingState]);

  // 数据加载完成后的回调
  const handleDataLoadComplete = useCallback((tabId: string) => {
    if (!contentId) return;
    
    let requestType: 'flashcards' | 'chapters' | 'summary' | 'quizGroups' | null = null;
    
    switch (tabId) {
      case 'flashcards':
        requestType = 'flashcards';
        break;
      case 'summary':
        requestType = 'summary';
        break;
      case 'chapters':
        requestType = 'chapters';
        break;
      case 'quiz':
        requestType = 'quizGroups';
        break;
    }

    if (requestType) {
      console.log(`✅ RightSidebar: Load complete for ${requestType}, contentId: ${contentId}`);
      setRequestingState(contentId, requestType, false);
      // 清除应该加载的状态
      setShouldLoadStates(prev => ({ ...prev, [requestType]: false }));
    }
  }, [contentId, setRequestingState]);

  // 当 hasValidPdf 变为 false 时，如果当前 tab 不是 chat，自动切换到 chat
  useEffect(() => {
    if (!hasValidPdf && activeTab !== 'chat') {
      setActiveTab('chat');
    }
  }, [hasValidPdf, activeTab, setActiveTab]);

  return (
    <div className="flex flex-col  h-full border-l py-2 dark:border-neutral-700 bg-white dark:bg-neutral-900">
      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <Tabs value={activeTab} onValueChange={handleInternalTabChange} className="flex flex-col flex-1 h-full">
        {/* Tab导航容器 - 包含展开按钮和tab列表 */}
        <div className="flex flex-row md:space-x-2 ml-2">
          {/* 展开/收起按钮 - 仅在桌面端显示且有有效PDF时显示 */}
          {hasValidPdf && onToggleArticle && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleArticle}
              className="items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:flex hidden bg-primary/5 text-muted-foreground"
            >
              {showArticleOnDesktop ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          )}

          {/* Tab列表 */}
          <TabsList className="inline-flex h-10 rounded-lg p-1 text-muted-foreground bg-primary/5 w-full max-w-full overflow-x-auto items-center justify-start">
            <div className="flex w-full items-center overflow-x-auto scrollbar-hide gap-2">
              <TabsTrigger
                value="chat"
                className="justify-center whitespace-nowrap rounded-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground w-full hover:text-foreground flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                {t('article.tabs.chat')}
              </TabsTrigger>
              {hasValidPdf && (
                <TabsTrigger
                  value="flashcards"
                  className="justify-center whitespace-nowrap rounded-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground w-full hover:text-foreground flex items-center gap-2"
                >
                  <Layers className="h-4 w-4" />
                  {t('article.tabs.flashcards')}
                </TabsTrigger>
              )}
              {hasValidPdf && (
                <TabsTrigger
                  value="quiz"
                  className="justify-center whitespace-nowrap rounded-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground w-full hover:text-foreground flex items-center gap-2"
                >
                  <HelpCircle className="h-4 w-4" />
                  {t('article.tabs.quiz')}
                </TabsTrigger>
              )}
              {hasValidPdf && (
                <TabsTrigger
                  value="summary"
                  className="justify-center whitespace-nowrap rounded-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground w-full hover:text-foreground flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  {t('article.tabs.summary')}
                </TabsTrigger>
              )}
              {hasValidPdf && (
                <TabsTrigger
                  value="chapters"
                  className="justify-center whitespace-nowrap rounded-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground w-full hover:text-foreground flex items-center gap-2"
                >
                  <BookOpen className="h-4 w-4" />
                  {t('article.tabs.chapters')}
                </TabsTrigger>
              )}
            </div>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden">
          {/* Chat组件始终渲染，但只在活跃时显示 */}
          <div className={`h-full ${activeTab === 'chat' ? 'block' : 'hidden'}`}>
            <ChatComponent
              contentId={contentId || 0}
              initialInputValue={newArticleChatData?.initialInputValue}
              initialSelectedModelId={newArticleChatData?.initialSelectedModelId}
              autoSend={newArticleChatData?.autoSend || false}
              autoSendType={newArticleChatData?.type}
              onAutoSendComplete={onAutoSendComplete}
              contentData={contentData}
            />
          </div>
          {hasValidPdf && (
            <TabsContent value="flashcards" className="p-0 m-0 h-full">
              <FlashcardsComponent
                contentId={contentId || 0}
                skipAutoLoad={true}
                isLoading={contentId ? isRequesting(contentId, 'flashcards') : false}
                shouldLoad={shouldLoadStates.flashcards || false}
                onLoadComplete={() => handleDataLoadComplete('flashcards')}
              />
            </TabsContent>
          )}
          {hasValidPdf && (
            <TabsContent value="quiz" className="p-0 m-0 h-full">
              <QuizComponent
                contentId={contentId || 0}
                skipAutoLoad={true}
                isLoading={contentId ? isRequesting(contentId, 'quizGroups') : false}
                shouldLoad={shouldLoadStates.quizGroups || false}
                onLoadComplete={() => handleDataLoadComplete('quiz')}
              />
            </TabsContent>
          )}
          {hasValidPdf && (
            <TabsContent value="summary" className="p-0 m-0 h-full">
              <SummaryComponent
                contentId={contentId || 0}
                skipAutoLoad={true}
                isLoading={contentId ? isRequesting(contentId, 'summary') : false}
                shouldLoad={shouldLoadStates.summary || false}
                onLoadComplete={() => handleDataLoadComplete('summary')}
              />
            </TabsContent>
          )}
          {hasValidPdf && (
            <TabsContent value="chapters" className="p-0 m-0 h-full">
              <ChaptersComponent
                contentId={contentId || 0}
                skipAutoLoad={true}
                isLoading={contentId ? isRequesting(contentId, 'chapters') : false}
                shouldLoad={shouldLoadStates.chapters || false}
                onLoadComplete={() => handleDataLoadComplete('chapters')}
              />
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
}
