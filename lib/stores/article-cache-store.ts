import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Flashcard } from '@/types'
import type {
  ChapterSection,
  ContentData,
  NewArticleChat,
  RequestType
} from '@/lib/types/article'

// 重新导出类型以保持向后兼容
export type { ChapterSection, ContentData, NewArticleChat, RequestType } from '@/lib/types/article';

// 内容数据缓存 Store
interface ContentCacheState {
  contentData: Record<number, ContentData>
  setContentData: (contentId: number, contentData: ContentData) => void
  hasContentData: (contentId: number) => boolean
  getContentData: (contentId: number) => ContentData | undefined
  clearContentData: (contentId: number) => void
}

export const useContentCacheStore = create<ContentCacheState>()(
  persist(
    (set, get) => ({
      contentData: {},

      setContentData: (contentId, contentData) =>
        set((state) => ({
          contentData: { ...state.contentData, [contentId]: contentData }
        })),

      hasContentData: (contentId) => {
        return Boolean(get().contentData[contentId])
      },

      getContentData: (contentId) => {
        return get().contentData[contentId]
      },

      clearContentData: (contentId) =>
        set((state) => {
          const newContentData = { ...state.contentData }
          delete newContentData[contentId]
          return { contentData: newContentData }
        })
    }),
    {
      name: 'content-cache-storage',
      version: 1
    }
  )
)

// 文章数据缓存 Store
interface ArticleDataState {
  flashcards: Record<number, Flashcard[]>
  chapters: Record<number, ChapterSection[]>
  summaries: Record<number, string>
  quizGroups: Record<number, any[]>

  setFlashcards: (contentId: number, flashcards: Flashcard[]) => void
  setChapters: (contentId: number, chapters: ChapterSection[]) => void
  setSummary: (contentId: number, summary: string) => void
  setQuizGroups: (contentId: number, quizGroups: any[]) => void

  hasFlashcards: (contentId: number) => boolean
  hasChapters: (contentId: number) => boolean
  hasSummary: (contentId: number) => boolean
  hasQuizGroups: (contentId: number) => boolean

  clearArticleData: (contentId: number) => void
  clearAllArticleData: () => void
}

export const useArticleDataStore = create<ArticleDataState>()(
  persist(
    (set, get) => ({
      flashcards: {},
      chapters: {},
      summaries: {},
      quizGroups: {},

      setFlashcards: (contentId, flashcards) =>
        set((state) => ({
          flashcards: { ...state.flashcards, [contentId]: flashcards }
        })),

      setChapters: (contentId, chapters) =>
        set((state) => ({
          chapters: { ...state.chapters, [contentId]: chapters }
        })),

      setSummary: (contentId, summary) =>
        set((state) => ({
          summaries: { ...state.summaries, [contentId]: summary }
        })),

      setQuizGroups: (contentId, quizGroups) =>
        set((state) => ({
          quizGroups: { ...state.quizGroups, [contentId]: quizGroups }
        })),

      hasFlashcards: (contentId) => {
        const flashcards = get().flashcards[contentId]
        return flashcards && flashcards.length > 0
      },

      hasChapters: (contentId) => {
        const chapters = get().chapters[contentId]
        return chapters && chapters.length > 0
      },

      hasSummary: (contentId) => {
        const summary = get().summaries[contentId]
        return Boolean(summary && summary.length > 0)
      },

      hasQuizGroups: (contentId) => {
        const quizGroups = get().quizGroups[contentId]
        return quizGroups && quizGroups.length > 0
      },

      clearArticleData: (contentId) =>
        set((state) => {
          const newFlashcards = { ...state.flashcards }
          const newChapters = { ...state.chapters }
          const newSummaries = { ...state.summaries }
          const newQuizGroups = { ...state.quizGroups }

          delete newFlashcards[contentId]
          delete newChapters[contentId]
          delete newSummaries[contentId]
          delete newQuizGroups[contentId]

          return {
            flashcards: newFlashcards,
            chapters: newChapters,
            summaries: newSummaries,
            quizGroups: newQuizGroups
          }
        }),

      clearAllArticleData: () =>
        set(() => ({
          flashcards: {},
          chapters: {},
          summaries: {},
          quizGroups: {}
        }))
    }),
    {
      name: 'article-data-storage',
      version: 1
    }
  )
)

// 请求状态管理 Store
interface RequestStateStore {
  requestingStates: Record<number, Record<RequestType, boolean>>
  setRequestingState: (contentId: number, type: RequestType, requesting: boolean) => void
  isRequesting: (contentId: number, type: RequestType) => boolean
  shouldStartRequest: (contentId: number, type: RequestType) => boolean
  clearRequestStates: (contentId: number) => void
}

export const useRequestStateStore = create<RequestStateStore>((set, get) => ({
  requestingStates: {},

  setRequestingState: (contentId, type, requesting) =>
    set((state) => ({
      requestingStates: {
        ...state.requestingStates,
        [contentId]: {
          ...(state.requestingStates[contentId] || {
            flashcards: false,
            chapters: false,
            summary: false,
            quizGroups: false
          }),
          [type]: requesting
        }
      }
    })),

  isRequesting: (contentId, type) => {
    const contentRequestingState = get().requestingStates[contentId]
    return contentRequestingState ? contentRequestingState[type] : false
  },

  shouldStartRequest: (contentId, type) => {
    const isCurrentlyRequesting = get().requestingStates[contentId]?.[type] || false

    // 检查是否已有数据
    const articleDataStore = useArticleDataStore.getState()
    let hasData = false
    switch (type) {
      case 'flashcards':
        hasData = articleDataStore.hasFlashcards(contentId)
        break
      case 'chapters':
        hasData = articleDataStore.hasChapters(contentId)
        break
      case 'summary':
        hasData = articleDataStore.hasSummary(contentId)
        break
      case 'quizGroups':
        hasData = articleDataStore.hasQuizGroups(contentId)
        break
    }

    // 只有在没有数据且没有正在请求时才应该发起请求
    return !hasData && !isCurrentlyRequesting
  },

  clearRequestStates: (contentId) =>
    set((state) => {
      const newRequestingStates = { ...state.requestingStates }
      delete newRequestingStates[contentId]
      return { requestingStates: newRequestingStates }
    })
}))

// 新文章聊天 Store
interface NewArticleChatState {
  newArticleChat: Record<number, NewArticleChat>
  setNewArticleChat: (contentId: number, newArticleChat: NewArticleChat) => void
  getNewArticleChat: (contentId: number) => NewArticleChat | undefined
  clearNewArticleChat: (contentId: number) => void
  clearAllNewArticleChat: () => void
}

export const useNewArticleChatStore = create<NewArticleChatState>()(
  persist(
    (set, get) => ({
      newArticleChat: {},

      setNewArticleChat: (contentId, newArticleChat) =>
        set((state) => ({
          newArticleChat: { ...state.newArticleChat, [contentId]: newArticleChat }
        })),

      getNewArticleChat: (contentId) => {
        return get().newArticleChat[contentId]
      },

      clearNewArticleChat: (contentId) =>
        set((state) => {
          const newNewArticleChat = { ...state.newArticleChat }
          delete newNewArticleChat[contentId]
          return { newArticleChat: newNewArticleChat }
        }),

      clearAllNewArticleChat: () =>
        set(() => ({ newArticleChat: {} }))
    }),
    {
      name: 'new-article-chat-storage',
      version: 1
    }
  )
)

// 组合 hook - 提供向后兼容的接口
export const useArticleCacheStore = () => {
  const contentCache = useContentCacheStore()
  const articleData = useArticleDataStore()
  const requestState = useRequestStateStore()
  const newArticleChat = useNewArticleChatStore()

  return {
    // Content data 方法
    ...contentCache,

    // Article data 方法
    ...articleData,

    // Request state 方法
    ...requestState,

    // New article chat 方法
    ...newArticleChat,

    // 统一的清除方法
    clearContentCache: (contentId: number) => {
      contentCache.clearContentData(contentId)
      articleData.clearArticleData(contentId)
      requestState.clearRequestStates(contentId)
      newArticleChat.clearNewArticleChat(contentId)
    },

    clearAllCache: () => {
      articleData.clearAllArticleData()
      newArticleChat.clearAllNewArticleChat()
    }
  }
}